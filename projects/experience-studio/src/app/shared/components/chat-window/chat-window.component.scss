@mixin disabledProperty($opacity, $pointer, $cursor) {
  opacity: $opacity !important;
  pointer-events: $pointer !important;
  cursor: $cursor !important;
}

.chat-wrapper {
  width: 100%;
  height: 100vh;
  padding: 16px;
  padding-bottom: 90px;
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
  overflow: hidden;
  border-radius: 0px 0px 12px 12px;
  word-wrap: break-word;
  position: relative; /* Required for absolute positioned scroll button */
  overflow-wrap: break-word;
  white-space: pre-wrap;
  margin: 0 auto;
  position: relative;

  &.dark {
    border: 1px solid var(--color-border-dark);
    background: var(--Dark---60, var(--color-background-dark-60));
  }

  &.light {
    border: 1px solid var(--color-border-light);
    background: var(--Light---60, var(--color-background-light-60));
  }
}

.chat-messages {
  flex: 1;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  gap: 12px;
  padding-right: 6px;
  scrollbar-width: thin;
  scrollbar-color: rgba(0, 0, 0, 0.2) transparent;
  scroll-behavior: smooth; /* Enable smooth scrolling */
  position: relative; /* Required for absolute positioned scroll button */

  // Enable text selection in chat messages
  user-select: text;
  -webkit-user-select: text;
  -moz-user-select: text;
  -ms-user-select: text;

  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    background: transparent;
  }

  &::-webkit-scrollbar-thumb {
    background-color: var(--color-background-overlay);
    border-radius: 3px;
  }

  // Empty state placeholder styling
  .no-messages-placeholder {
    flex: 1;
    min-height: 300px;
    color: var(--text-secondary);

    .placeholder-icon {
      font-size: 3rem;
      opacity: 0.6;
    }

    .placeholder-title {
      font-size: 1.5rem;
      font-weight: 600;
      color: var(--text-primary);
    }

    .placeholder-description {
      font-size: 1rem;
      opacity: 0.8;
      max-width: 400px;
    }
  }
}

// Generating indicator above prompt bar
.generating-indicator {
  display: flex;
  justify-content: flex-start; // Move to full left
  align-items: center;

  .generating-stepper {
    display: flex;
    align-items: center;
    gap: 8px; // Increased gap for better spacing with spinner

    // Exact same spinner from stepper component
    .modern-loading-spinner {
      width: 20px; // Slightly smaller than stepper (24px)
      height: 20px;
      position: relative;
      display: flex;
      align-items: center;
      justify-content: center;
      flex-shrink: 0;

      .spinner-ring {
        position: absolute;
        width: 20px;
        height: 20px;
        border-radius: 50%;
        border: 2.5px solid transparent;
        border-top-color: #6566cd;
        border-bottom-color: #e30a6d;
        filter: drop-shadow(0 0 1px rgba(101, 102, 205, 0.3));
        animation: spin-ring 1.5s ease-in-out infinite;
      }

      .spinner-core {
        width: 10px;
        height: 10px;
        border-radius: 50%;
        background: linear-gradient(135deg, #6566cd 0%, #e30a6d 100%);
        box-shadow: 0 0 8px rgba(229, 10, 109, 0.5);
        animation: pulse 1.5s ease-in-out infinite alternate;
      }
    }

    .generating-text {
      font-size: 12px; // Smaller font size
      color: var(--color-text-secondary, #666);
      font-weight: 500;
      position: relative;
      overflow: hidden;
      background: linear-gradient(
        90deg,
        var(--color-text-secondary, #666) 25%,
        var(--code-viewer-bg) 50%,
        var(--color-text-secondary, #666) 75%
      );
      background-size: 200% 100%;
      background-clip: text;
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      animation: minimal-text-shine 1.5s ease-in-out infinite;
    }
  }

  // Dark theme support
  &.dark {
    .generating-stepper {
      .generating-text {
        color: var(--color-text-secondary-dark, #ccc);

        // Dark theme shining animation
        background: linear-gradient(
          90deg,
          var(--color-text-secondary-dark, #ccc) 25%,
          var(--code-viewer-bg) 50%,
          var(--color-text-secondary-dark, #ccc) 75%
        );
        background-size: 200% 100%;
        background-clip: text;
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;

        &::after {
          background: linear-gradient(
            90deg,
            transparent,
            rgba(156, 39, 176, 0.4),
            transparent
          );
        }
      }
    }
  }
}

// Regeneration progress indicator above prompt bar
.regeneration-progress-indicator {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  margin-bottom: 8px;

  .regeneration-stepper {
    display: flex;
    align-items: center;
    gap: 8px;

    // Exact same spinner from stepper component
    .modern-loading-spinner {
      width: 20px;
      height: 20px;
      position: relative;
      display: flex;
      align-items: center;
      justify-content: center;
      flex-shrink: 0;

      .spinner-ring {
        position: absolute;
        width: 20px;
        height: 20px;
        border-radius: 50%;
        border: 2.5px solid transparent;
        border-top-color: #6566cd;
        border-bottom-color: #e30a6d;
        filter: drop-shadow(0 0 1px rgba(101, 102, 205, 0.3));
        animation: spin-ring 1.5s ease-in-out infinite;
      }

      .spinner-core {
        width: 10px;
        height: 10px;
        border-radius: 50%;
        background: linear-gradient(135deg, #6566cd 0%, #e30a6d 100%);
        box-shadow: 0 0 8px rgba(229, 10, 109, 0.5);
        animation: pulse 1.5s ease-in-out infinite alternate;
      }
    }

    .regeneration-text {
      font-size: 12px;
      color: var(--color-text-secondary, #666);
      font-weight: 500;
      position: relative;
      overflow: hidden;
      max-width: 400px;
      word-wrap: break-word;
      background: linear-gradient(
        90deg,
        var(--color-text-secondary, #666) 25%,
        var(--code-viewer-bg) 50%,
        var(--color-text-secondary, #666) 75%
      );
      background-size: 200% 100%;
      background-clip: text;
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      animation: minimal-text-shine 1.5s ease-in-out infinite;
    }
  }

  // Dark theme support
  &.dark {
    .regeneration-stepper {
      .regeneration-text {
        color: var(--color-text-secondary-dark, #ccc);

        // Dark theme shining animation
        background: linear-gradient(
          90deg,
          var(--color-text-secondary-dark, #ccc) 25%,
          var(--code-viewer-bg) 50%,
          var(--color-text-secondary-dark, #ccc) 75%
        );
        background-size: 200% 100%;
        background-clip: text;
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;

        &::after {
          background: linear-gradient(
            90deg,
            transparent,
            rgba(156, 39, 176, 0.4),
            transparent
          );
        }
      }
    }
  }
}

@keyframes spin-clockwise {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

// Keyframe animations for the spinner and text effects
@keyframes spin-ring {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

@keyframes pulse {
  0% {
    opacity: 0.8;
    transform: scale(1);
  }
  100% {
    opacity: 1;
    transform: scale(1.1);
  }
}

@keyframes minimal-text-shine {
  0% {
    background-position: 200%;
  }
  100% {
    background-position:  0;
  }
}


awe-prompt-bar {
  width: 100% !important; // Ensure the width is 100%
  gap: 8px;
  display: flex;
  align-items: center;
  box-sizing: border-box;
  margin-bottom: 0.5rem;
  border-radius: var(--4x, 16px);

  &.disabled-prompt-bar {
    @include disabledProperty(0.7, none, not-allowed);

    ::ng-deep {
      textarea {
        @include disabledProperty(0.7, none, not-allowed);
        background-color: transparent !important;
      }

      .awe-icons {
        @include disabledProperty(0.7, none, not-allowed);
      }

      .prompt-bar-actions {
        @include disabledProperty(0.7, none, not-allowed);
      }
    }

    &::after {
      // content: 'Prompt bar will be enabled after code generation is complete';
      position: absolute;
      bottom: -20px;
      left: 50%;
      transform: translateX(-50%);
      font-size: 12px;
      color: var(--text-secondary);
      white-space: nowrap;
      background-color: var(--color-background-light);
      padding: 2px 8px;
      border-radius: 4px;
      opacity: 0;
      transition: opacity 0.3s ease;
      pointer-events: none;
    }

    &:hover::after {
      opacity: 1;
    }
  }
}

.ai-card {
  display: flex;
  width: auto;
  box-sizing: border-box;
  justify-content: flex-start;

  .awe-card {
    padding: 8px !important;
    border-radius: 8px;
    max-width: 80%; // ✅ Responsive width
    width: fit-content;
    min-height: auto;
    word-break: break-word;
    overflow-wrap: break-word;
    white-space: normal;
    padding-top: 0px;

    // Ensure consistent card size during typewriting
    min-height: 40px; // Minimum height to prevent card resizing
    display: flex;
    align-items: flex-start;

    .markdown-content {
      width: 100%;
      min-height: 24px; // Minimum content height
    }
  }

// Removed stepper-card styles as stepper is now standalone
}

// Inline Stepper Container Styles (integrated in chat flow)
.stepper-container-inline {
  display: flex;
  width: auto;
  box-sizing: border-box;
  justify-content: flex-start; // Align like AI cards (left)
  max-width: 80%;
  border: 1px solid var(--code-viewer-border) !important;
  border-radius: 8px;
  background-color: var(--chat-window-card-bg-color) !important;
  padding: 10px 20px;
  margin-bottom: 16px; // Consistent spacing with other cards

  // Fix overflow issues
  overflow: visible; // Allow content to expand naturally
  min-width: 300px; // Ensure minimum readable width
  width: auto; // Allow dynamic width based on content

  // Styles for the inline app-vertical-stepper component
  app-vertical-stepper {
    display: block;
    width: 100%;
    overflow: visible; // Prevent internal overflow

    ::ng-deep {
      .vertical-stepper {
        margin: 0;
        padding: 20px;
        border-radius: 12px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        overflow: visible; // Allow content to expand
        min-height: auto; // Remove any height constraints

        // Theme-specific background colors
        .light & {
          background-color: var(--color-background-light) !important;
          border-color: var(--color-border-light) !important;
        }

        .dark & {
          background-color: var(--color-background-dark) !important;
          border-color: var(--color-border-dark) !important;
        }
      }
    }
  }

  // ENHANCED: Error stepper container styling
  .error-stepper-container {
    margin-top: 12px;
    width: 100%;

    &.light {
      background-color: transparent;
    }

    &.dark {
      background-color: transparent;
    }

    app-vertical-stepper {
      display: block;
      width: 100%;
      overflow: visible;

      ::ng-deep {
        .vertical-stepper {
          margin: 0;
          padding: 16px;
          border-radius: 8px;
          box-shadow: 0 1px 4px rgba(0, 0, 0, 0.08);
          overflow: visible;
          min-height: auto;
          border: 1px solid;

          // Theme-specific styling for error stepper
          .light & {
            background-color: #fef2f2 !important;
            border-color: #fecaca !important;
          }

          .dark & {
            background-color: #451a1a !important;
            border-color: #7f1d1d !important;
          }
        }
      }
    }
  }
}

// Legacy Standalone Stepper Container Styles (kept for backward compatibility)
.standalone-stepper-container {
  max-width: 80%;
  border: 1px solid var(--code-viewer-border) !important;
  border-radius: 8px;
  background-color: var(--chat-window-card-bg-color) !important;
  padding: 10px 20px;
  box-sizing: border-box;

  // Fix overflow issues
  overflow: visible; // Allow content to expand naturally
  min-width: 300px; // Ensure minimum readable width
  width: auto; // Allow dynamic width based on content

  // Styles for the standalone app-vertical-stepper component
  app-vertical-stepper {
    display: block;
    width: 100%;
    overflow: visible; // Prevent internal overflow

    ::ng-deep {
      .vertical-stepper {
        margin: 0;
        padding: 20px;
        border-radius: 12px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        overflow: visible; // Allow content to expand
        min-height: auto; // Remove any height constraints

        // Theme-specific background colors
        .light & {
          background-color: var(--color-background-light, #ffffff);
          border: 1px solid var(--color-border-light, #e0e0e0);
        }

        .dark & {
          background-color: var(--color-background-dark, #1a1a1a);
          border: 1px solid var(--color-border-dark, #333333);
        }

        .stepper-container {
          gap: 40px;
          align-items: flex-start;
        }

        .step-circle {
          width: 24px;
          height: 24px;
        }

        .step-line-container {
          left: 12px;
          top: 32px;
          width: 2px;
          transition: opacity 0.3s ease-out, visibility 0s 0.3s;
        }

        .step-line {
          background: linear-gradient(180deg, #9C27B0 0%, #E91E63 100%);

          &.animating {
            animation: connectLine 1.5s cubic-bezier(0.4, 0, 0.2, 1) forwards,
                       pulseLine 2s ease-in-out infinite;
          }
        }

        .completed .step-circle {
          background-color: #9C27B0;
          border: none;
        }

        .step-icon {
          width: 18px;
          height: 18px;
        }

        /* Hide line when step is collapsed */
        .stepper-item.collapsed .step-line-container {
          opacity: 0;
          visibility: hidden;
          transition: opacity 0.3s ease-out, visibility 0s 0.3s;
        }

        /* Next step styling */
        .stepper-item.next {
          opacity: 0.6;
          pointer-events: none;
        }

        /* Smooth animations */
        .stepper-item {
          transition: all 0.4s cubic-bezier(0.25, 0.1, 0.25, 1);
          align-self: flex-start;
          width: 100%;
          will-change: transform, opacity;
          transform: translateZ(0);
          overflow: visible; // Allow content to expand
        }

        // Fix text wrapping and overflow in step content
        .step-content {
          flex: 1;
          min-width: 0; // Allow flex item to shrink
          overflow: visible; // Prevent content clipping
          word-wrap: break-word;
          overflow-wrap: break-word;
        }

        .step-content-inner {
          width: 100%;
          overflow: visible;
        }

        .step-description {
          transition: max-height 0.4s cubic-bezier(0.25, 0.1, 0.25, 1),
                      opacity 0.3s ease-out,
                      margin 0.3s ease-out;
          overflow: visible; // Remove overflow hidden that causes clipping
          max-height: none; // Remove height constraint to prevent overflow
          opacity: 1;
          will-change: max-height, opacity, margin;
          transform: translateZ(0);
          word-wrap: break-word;
          overflow-wrap: break-word;
          white-space: normal; // Allow text to wrap

          // Ensure markdown content wraps properly
          ::ng-deep {
            p, div, span {
              word-wrap: break-word;
              overflow-wrap: break-word;
              white-space: normal;
            }
          }
        }

        .step-description.collapsed {
          max-height: 0;
          opacity: 0;
          margin-top: 0;
          margin-bottom: 0;
          overflow: hidden; // Only hide overflow when collapsed
          transition: max-height 0.4s cubic-bezier(0.25, 0.1, 0.25, 1),
                      opacity 0.3s ease-out,
                      margin 0.3s ease-out;
        }

        .step-title {
          word-wrap: break-word;
          overflow-wrap: break-word;
          white-space: normal;
        }

        @keyframes pulseLine {
          0% {
            opacity: 1;
          }
          50% {
            opacity: 0.6;
          }
          100% {
            opacity: 1;
          }
        }
      }
    }
  }

  // AI Loading Content Styles
  .ai-loading-content {
    display: flex;
    align-items: center;
    padding: 12px 0;
    min-height: 40px;
  }

  .ai-loading-dots {
    display: flex;
    align-items: center;
    gap: 4px;
  }

  .ai-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background-color: var(--color-primary, #9C27B0);
    animation: ai-loading-bounce 1.4s ease-in-out infinite both;

    &:nth-child(1) {
      animation-delay: -0.32s;
    }

    &:nth-child(2) {
      animation-delay: -0.16s;
    }

    &:nth-child(3) {
      animation-delay: 0s;
    }
  }

  @keyframes ai-loading-bounce {
    0%, 80%, 100% {
      transform: scale(0.8);
      opacity: 0.5;
    }
    40% {
      transform: scale(1);
      opacity: 1;
    }
  }

  // ENHANCED: Intro message loading styles for code generation
  .intro-loading-container {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-top: 8px;
    padding: 8px 0;
  }

  .intro-loading-dots {
    display: flex;
    align-items: center;
    gap: 4px;

    .dot {
      width: 6px;
      height: 6px;
      border-radius: 50%;
      background-color: var(--color-primary, #9C27B0);
      animation: intro-loading-bounce 1.4s ease-in-out infinite both;

      &:nth-child(1) {
        animation-delay: -0.32s;
      }

      &:nth-child(2) {
        animation-delay: -0.16s;
      }

      &:nth-child(3) {
        animation-delay: 0s;
      }
    }
  }

  .intro-loading-text {
    font-size: 0.875rem;
    color: var(--color-text-secondary, #666);
    font-style: italic;
  }

  // ENHANCED: Main API loading styles for code generation
  .main-loading-container {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-top: 8px;
    padding: 8px 0;
  }

  .main-loading-dots {
    display: flex;
    align-items: center;
    gap: 4px;

    .dot {
      width: 6px;
      height: 6px;
      border-radius: 50%;
      background-color: var(--color-success, #28a745);
      animation: main-loading-bounce 1.4s ease-in-out infinite both;

      &:nth-child(1) {
        animation-delay: -0.32s;
      }

      &:nth-child(2) {
        animation-delay: -0.16s;
      }

      &:nth-child(3) {
        animation-delay: 0s;
      }
    }
  }

  .main-loading-text {
    font-size: 0.875rem;
    color: var(--color-text-secondary, #666);
    font-style: italic;
  }

  // ENHANCED: Loading animations for intro and main phases
  @keyframes intro-loading-bounce {
    0%, 80%, 100% {
      transform: scale(0.7);
      opacity: 0.4;
    }
    40% {
      transform: scale(1);
      opacity: 1;
    }
  }

  @keyframes main-loading-bounce {
    0%, 80%, 100% {
      transform: scale(0.7);
      opacity: 0.4;
    }
    40% {
      transform: scale(1);
      opacity: 1;
    }
  }

  // ENHANCED: Dark theme support for intro and main loading indicators
  &.dark {
    .intro-loading-text,
    .main-loading-text {
      color: var(--color-text-secondary-dark, #ccc);
    }

    .intro-loading-dots .dot {
      background-color: var(--color-primary-dark, #bb86fc);
    }

    .main-loading-dots .dot {
      background-color: var(--color-success-dark, #4caf50);
    }
  }

  // Loading card specific styles
  &.loading-card {
    .awe-card {
      background-color: var(--color-background-secondary-light, #f8f9fa);
      border: 1px solid var(--color-border-light, #e0e0e0);
    }

    &.dark .awe-card {
      background-color: var(--color-background-secondary-dark, #2a2a2a);
      border: 1px solid var(--color-border-dark, #404040);
    }
  }

  .markdown-content {
    width: 100%;

    // Enable text selection for all markdown content
    user-select: text !important;
    -webkit-user-select: text !important;
    -moz-user-select: text !important;
    -ms-user-select: text !important;

    ::ng-deep {
      // Enable text selection for all nested elements
      * {
        user-select: text !important;
        -webkit-user-select: text !important;
        -moz-user-select: text !important;
        -ms-user-select: text !important;
      }

      // Headings
      h1,
      h2,
      h3,
      h4,
      h5,
      h6 {
        margin-top: 1em;
        margin-bottom: 0.5em;
        font-weight: 600;
        line-height: 1.25;
      }

      h1 {
        font-size: 1.5em;
        border-bottom: 1px solid var(--color-border-light);
        padding-bottom: 0.3em;
      }

      h2 {
        font-size: 1.25em;
        border-bottom: 1px solid var(--color-border-light);
        padding-bottom: 0.3em;
      }

      h3 {
        font-size: 1.1em;
      }

      // Lists
      ul,
      ol {
        padding-left: 2em;
        margin-top: 0.5em;
        margin-bottom: 0.5em;
      }

      li {
        margin: 0.25em 0;
      }

      // Code blocks
      pre {
        background-color: rgba(0, 0, 0, 0.05);
        border-radius: 4px;
        padding: 0.5em;
        overflow-x: auto;
        margin: 0.5em 0;

        code {
          background-color: transparent;
          padding: 0;
          border-radius: 0;
          font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
          font-size: 0.9em;

          // Enable text selection in code blocks
          user-select: text !important;
          -webkit-user-select: text !important;
          -moz-user-select: text !important;
          -ms-user-select: text !important;

          // Syntax highlighting
          .hljs-keyword {
            color: #569cd6;
          }

          .hljs-string {
            color: #ce9178;
          }

          .hljs-comment {
            color: #6a9955;
          }

          .hljs-function {
            color: #dcdcaa;
          }

          .hljs-number {
            color: #b5cea8;
          }

          .hljs-operator {
            color: #d4d4d4;
          }

          .hljs-class {
            color: #4ec9b0;
          }

          .hljs-variable {
            color: #9cdcfe;
          }
        }
      }

      code {
        font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
        background-color: rgba(0, 0, 0, 0.05);
        border-radius: 3px;
        padding: 0.2em 0.4em;
        font-size: 0.9em;
      }

      // Tables
      table {
        border-collapse: collapse;
        width: 100%;
        margin: 1em 0;
      }

      th,
      td {
        border: 1px solid var(--color-border-light);
        padding: 0.5em;
        text-align: left;
      }

      th {
        background-color: rgba(0, 0, 0, 0.05);
      }

      // Blockquotes
      blockquote {
        border-left: 4px solid var(--color-border-light);
        margin: 0.5em 0;
        padding: 0 1em;
        color: var(--text-secondary);
      }

      // Links
      a {
        color: var(--color-primary);
        text-decoration: none;

        &:hover {
          text-decoration: underline;
        }
      }

      // Images
      img {
        max-width: 100%;
        height: auto;
        margin: 0.5em 0;
      }

      // Horizontal rule
      hr {
        border: 0;
        border-top: 1px solid var(--color-border-light);
        margin: 1em 0;
      }

      // Paragraphs
      p {
        margin: 0.5em 0;
        line-height: 1.5;
      }
    }
  }
}

.user-card {
  display: flex;
  width: auto;
  box-sizing: border-box;
  justify-content: flex-end;

  .awe-card {
    padding: 0px 8px !important;
    border-radius: 8px;
    max-width: 80%; // ✅ Responsive width
    width: fit-content;
    min-height: auto;
    word-break: break-word;
    overflow-wrap: break-word;
    white-space: normal;
    padding-top: 0px;

    // Ensure content takes full width of the card
    .awe-card__content {
      width: 100%;
    }
  }

  // Styling for the selected image
  .selected-image {
    margin: 8px 0;
    cursor: pointer;
    display: flex;
    justify-content: flex-end; // Right align the image container
    width: 100%; // Take full width of the parent


    .image-preview {
      border-radius: 8px;
      overflow: hidden;
      width: 100%; // Take full width of the container
      cursor: pointer;
      transition: transform 0.2s ease;

      &:hover {
        transform: scale(1.02);
      }

      img.thumbnail-image {
        width: 100%; // Take full width of the preview container
        max-height: 300px; // Increased height for better visibility
        display: block;
        object-fit: contain; // Changed to contain to preserve aspect ratio
        border-radius: 4px;
        transition: all 0.2s ease;
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);

        &:hover {
          box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
        }
      }
    }
  }

  .markdown-content {
    width: 100%;

    // Enable text selection for all markdown content
    user-select: text !important;
    -webkit-user-select: text !important;
    -moz-user-select: text !important;
    -ms-user-select: text !important;

    ::ng-deep {
      // Enable text selection for all nested elements
      * {
        user-select: text !important;
        -webkit-user-select: text !important;
        -moz-user-select: text !important;
        -ms-user-select: text !important;
      }

      // Headings
      h1,
      h2,
      h3,
      h4,
      h5,
      h6 {
        margin-top: 1em;
        margin-bottom: 0.5em;
        font-weight: 600;
        line-height: 1.25;
      }

      h1 {
        font-size: 1.5em;
        border-bottom: 1px solid var(--color-border-light);
        padding-bottom: 0.3em;
      }

      h2 {
        font-size: 1.25em;
        border-bottom: 1px solid var(--color-border-light);
        padding-bottom: 0.3em;
      }

      h3 {
        font-size: 1.1em;
      }

      // Lists
      ul,
      ol {
        padding-left: 2em;
        margin-top: 0.5em;
        margin-bottom: 0.5em;
      }

      li {
        margin: 0.25em 0;
      }

      // Code blocks
      pre {
        background-color: rgba(0, 0, 0, 0.05);
        border-radius: 4px;
        padding: 0.5em;
        overflow-x: auto;
        margin: 0.5em 0;
      }

      code {
        font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
        background-color: rgba(0, 0, 0, 0.05);
        border-radius: 3px;
        padding: 0.2em 0.4em;
        font-size: 0.9em;
      }

      // Tables
      table {
        border-collapse: collapse;
        width: 100%;
        margin: 1em 0;
      }

      th,
      td {
        border: 1px solid var(--color-border-light);
        padding: 0.5em;
        text-align: left;
      }

      th {
        background-color: rgba(0, 0, 0, 0.05);
      }

      // Blockquotes
      blockquote {
        border-left: 4px solid var(--color-border-light);
        margin: 0.5em 0;
        padding: 0 1em;
        color: var(--text-secondary);
      }

      // Links
      a {
        color: var(--color-primary);
        text-decoration: none;

        &:hover {
          text-decoration: underline;
        }
      }

      // Images
      img {
        max-width: 100%;
        height: auto;
        margin: 0.5em 0;
      }

      // Horizontal rule
      hr {
        border: 0;
        border-top: 1px solid var(--color-border-light);
        margin: 1em 0;
      }

      // Paragraphs
      p {
        margin: 0.5em 0;
        line-height: 1.5;
      }
    }
  }
}

// User (right aligned)
.user-card {
  justify-content: flex-end;

  &.dark .awe-card {
    background-color: var(--color-background-dark);
    color: var(--text-white);
  }

  &.light .awe-card {
    background-color: var(--Neutral-N-100, var(--color-background-light));
    color: var(--text-black);
  }
}

// AI (left aligned)
.ai-card {
  justify-content: flex-start;

  &.dark .awe-card {
    background-color: var(--color-background-secondary-light);
    color: var(--text-white);
  }

  &.light .awe-card {
    background-color: var(--Neutral-N-50, var(--color-background-light));
    color: var(--text-black);
  }
}

// Deep styles to ensure theme colors are applied
:host ::ng-deep {
  // Card styles - target all card variants
  .awe-card {
    border-radius: var(--3x, 8px) !important;
    color: var(--chat-window-text-color) !important;
  }

  // Force override for all cards in dark theme
  .chat-wrapper.dark {
    // All cards in dark theme
    .awe-card {
      background-color: var(--color-background-dark) !important;
      color: var(--text-white) !important;
      border-color: var(--color-border-dark) !important;
    }

    // User card specific styles
    .user-card .awe-card {
      background-color: var(--color-background-dark) !important;
      color: var(--text-white) !important;
    }

    // AI card specific styles
    .ai-card .awe-card {
      background-color: var(--color-background-secondary-light) !important;
      color: var(--text-white) !important;
    }
  }

  // Force override for all cards in light theme
  .chat-wrapper.light {
    // All cards in light theme
    .awe-card {
      color: var(--text-black) !important;
      border-color: var(--color-border-light) !important;
    }

    // User card specific styles
    .user-card .awe-card {
      background-color: var(--Neutral-N-100, var(--color-background-light)) !important;
      color: var(--text-black) !important;
    }

    // AI card specific styles
    .ai-card .awe-card {
      background-color: var(--Neutral-N-50, var(--color-background-light)) !important;
      color: var(--text-black) !important;
    }
  }

  // Ensure card content has proper padding
  .awe-card__content {
    padding: 10px !important;
  }

  // Override any theme classes added by the component library
  .awe-card--light {
    &.dark,
    .dark & {
      background-color: var(--color-background-dark) !important;
      color: var(--text-white) !important;
    }
  }

  .awe-card--dark {
    &.light,
    .light & {
      background-color: var(--color-background-light) !important;
      color: var(--text-black) !important;
    }
  }

  // Direct targeting of the awe-card element with highest specificity
  .chat-wrapper.dark .user-card awe-cards .awe-card {
    background-color: var(--color-background-dark) !important;
    color: var(--text-white) !important;
  }

  .chat-wrapper.dark .ai-card.stepper-card awe-cards .awe-card {
    background-color: var(--color-background-secondary-light) !important;
    color: var(--text-white) !important;
    max-width: 100% !important;
    width: 100% !important;
  }

  .chat-wrapper.dark .ai-card awe-cards .awe-card {
    background-color: var(--color-background-secondary-light) !important;
    color: var(--text-white) !important;
  }

  .chat-wrapper.light .ai-card.stepper-card awe-cards .awe-card {
    background-color: var(--Neutral-N-50, var(--color-background-light)) !important;
    color: var(--text-black) !important;
    max-width: 100% !important;
    width: 100% !important;
  }

  .chat-wrapper.light .user-card awe-cards .awe-card {
    background-color: var(--Neutral-N-100, var(--color-background-light)) !important;
    color: var(--text-black) !important;
  }

  .chat-wrapper.light .ai-card awe-cards .awe-card {
    background-color: var(--Neutral-N-50, var(--color-background-light)) !important;
    color: var(--text-black) !important;
  }

  // Override any inline styles that might be applied
  .awe-card[style*='background'] {
    .chat-wrapper.dark & {
      background-color: var(--color-background-dark) !important;
    }

    .chat-wrapper.light & {
      background-color: var(--color-background-light) !important;
    }
  }
}

// Intro Message Shimmer Container Styles - Completely transparent with only shimmer line visible
.ai-shimmer-container {
  width: 100%;
  max-width: 80%;
  margin: 10px 0;
  background: transparent; // Completely transparent
  border: none; // No border
  box-shadow: none; // No shadow

  &.dark {
    background: transparent;
    border: none;
    box-shadow: none;
  }

  &.light {
    background: transparent;
    border: none;
    box-shadow: none;
  }
}



// Image Preview Overlay
.preview-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1000;
  background-color: rgba(0, 0, 0, 0.7);
  display: flex;
  justify-content: center;
  align-items: center;
  animation: fadeIn 0.3s ease-in-out;

  .preview-content {
    background: var(--preview-page-bg-color, white);
    border-radius: 8px;
    max-width: 90vw;
    max-height: 90vh;
    overflow: hidden;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
    animation: scaleIn 0.3s ease-in-out;
    transition: all 0.3s ease-in-out;

    .preview-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 12px 16px;
      border-bottom: 1px solid var(--border-color, #eee);

      .preview-title {
        font-size: 16px;
        font-weight: 600;
        color: var(--pill-text-color, #333);
      }

      awe-icons {
        cursor: pointer;
        &:hover {
          color: var(--danger, red);
        }
      }
    }

    .preview-body {
      padding: 16px;
      display: flex;
      justify-content: center;
      align-items: center;
      max-height: calc(90vh - 60px);
      overflow: auto;

      img {
        max-width: 100%;
        max-height: 100%;
        object-fit: contain;
        border-radius: 4px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
      }
    }
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes scaleIn {
  from {
    transform: scale(0.9);
    opacity: 0;
  }
  to {
    transform: scale(1);
    opacity: 1;
  }
}

// UI Design loading indicator styles
.ui-design-loading-card {
  margin-bottom: 16px;

  .ui-design-loading-content {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px;

    .ui-design-loading-dots {
      display: flex;
      align-items: center;
      gap: 8px;

      .ui-design-dot {
        width: 8px;
        height: 8px;
        border-radius: 50%;
        background-color: var(--primary-color, #007bff);
        animation: ui-design-pulse 1.5s ease-in-out infinite both;

        &:nth-child(1) {
          animation-delay: -0.3s;
        }

        &:nth-child(2) {
          animation-delay: -0.15s;
        }

        &:nth-child(3) {
          animation-delay: 0s;
        }
      }
    }
  }
}

// Animation for UI Design loading dots
@keyframes ui-design-pulse {
  0%, 80%, 100% {
    transform: scale(0.8);
    opacity: 0.5;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}



// Generation result container styling
.generation-result-container {
  margin-top: 12px;
  padding-top: 8px;
  border-top: 1px solid rgba(0, 0, 0, 0.1);

  // Dark theme support
  .dark & {
    border-top-color: rgba(255, 255, 255, 0.1);
  }

  // Ensure proper spacing within AI cards
  .ai-card & {
    margin-top: 16px;
    padding-top: 12px;
  }

  // Smooth animation for accordion appearance
  app-generation-accordion {
    animation: fadeInUp 0.3s ease-out;
  }
}

// Generation card styling for separate cards
.generation-card {
  padding: 0; // Remove padding since accordion has its own
  border: none; // Remove border since accordion has its own
  background: transparent; // Let accordion handle background
  box-shadow: none; // Remove shadow since accordion has its own

  // Ensure cards flow naturally in chat sequence like normal AI cards
  display: flex;
  width: auto;
  box-sizing: border-box;
  justify-content: flex-start; // Align like AI cards

  // Remove any absolute or fixed positioning that might cause issues
  position: relative;

  // Ensure proper order in the document flow
  order: initial;

  // Smooth animation for card appearance
  animation: fadeInUp 0.4s ease-out;

  // Override ai-card styles for generation cards
  &.ai-card {
    background: transparent;
    border: none;
    box-shadow: none;
    padding: 0;

    // Ensure accordion takes proper width like other AI cards - super responsive
    app-generation-accordion {
      max-width: 80%; // Match AI card width
      width: 100%;
      min-width: 300px; // Ensure minimum readable width

      // Super responsive width adjustments with multiple breakpoints
      @media (max-width: 1200px) {
        max-width: 80%;
        min-width: 280px;
      }

      @media (max-width: 1024px) {
        max-width: 80%;
        min-width: 260px;
      }

      @media (max-width: 768px) {
        max-width: 80%;
        min-width: 240px;
      }

      @media (max-width: 640px) {
        max-width: 80%;
        min-width: 220px;
      }

      @media (max-width: 480px) {
        max-width: 80%;
        min-width: 200px;
      }

      @media (max-width: 360px) {
        max-width: 80%;
        min-width: 180px;
      }
    }
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// Custom content styles for chat-window prompt bar
.custom-content {
  width: 100%;

  .selected-files {
    width: 100%;
    .file-item {
      display: flex;
      align-items: center;
      justify-content: space-between;
      background-color: var(--preview-page-bg-color) !important;
      border: 1px solid var(--code-viewer-search-border) !important;
      border-radius: 8px;
      padding: 8px 12px;
      max-width: 200px;

      .file-info {
        display: flex;
        align-items: center;
        gap: 8px;
        flex: 1;

        .file-preview-image {
          width: 24px;
          height: 24px;
          object-fit: cover;
          border-radius: 4px;
          border: 1px solid var(--color-border-light);
        }

        .file-name {
          font-size: 14px;
          font-weight: 500;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          max-width: 120px;
        }
      }

      awe-icons {
        cursor: pointer;
        transition: transform 0.2s ease;

        &:hover {
          transform: scale(1.1);
        }
      }
    }
  }

  .attach-container {
    flex: 1;

    awe-icons {
      cursor: pointer;
      transition: all 0.3s ease-in-out;
      font-size: 24px;
      color: var(--icon-enabled-color) !important;

      &.disabled {
        opacity: 0.4;
        pointer-events: none;
        cursor: not-allowed;
        color: var(--icon-disabled-color) !important;
      }

      &:hover:not(.disabled) {
        transform: scale(1.1);
      }
    }
  }

  .enhance-icons {
    justify-content: flex-end;
    flex-shrink: 0;

    awe-icons {
      cursor: pointer;
      transition: all 0.3s ease-in-out;
      font-size: 24px;
      color: var(--icon-enabled-color) !important;

      &.disabled {
        opacity: 0.4;
        pointer-events: none;
        cursor: not-allowed;
        color: var(--icon-disabled-color) !important;
      }

      &:hover:not(.disabled) {
        transform: scale(1.1);
      }
    }

    .loading-spinner {
      min-width: auto;
      width: 24px;
      height: 24px;

      .spinner {
        width: 20px;
        height: 20px;
        border: 2px solid var(--icon-enabled-color);
        border-top: 2px solid transparent;
        border-radius: 50%;
        animation: spin 1s linear infinite;
      }
    }
  }
}

.tools-container {
  width: 100%;
  align-items: baseline !important;
}

.pills-container {
  flex: 1;
}

// Animation keyframes
@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@keyframes uiDesignPulse {
  0%, 100% {
    opacity: 0.3;
    transform: scale(1);
  }
  50% {
    opacity: 0.6;
    transform: scale(1.02);
  }
}

// Preview overlay styles (exact same as prompt-content)
.preview-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background-color: rgba(0, 0, 0, 0.8);
  display: flex;
  justify-content: center;
  align-items: center;
  animation: fadeIn 0.2s ease-in-out;
  backdrop-filter: blur(10px);
  z-index: 9999;

  .preview-content {
    position: relative;
    max-width: 90vw;
    max-height: 90vh;

    .preview-header {
      position: absolute;
      top: -40px;
      left: 0;
      right: 0;
      display: flex;
      justify-content: space-between;
      align-items: center;
      color: white;

      .preview-title {
        font-size: 16px;
        font-weight: 500;
      }

      awe-icons {
        cursor: pointer;
        ::ng-deep path {
          fill: white !important;
          stroke: white !important;
        }
      }
    }

    .preview-body {
      img {
        max-width: 90vw;
        max-height: 90vh;
        object-fit: contain;
        animation: fadeIn 0.3s ease-in-out;
      }
    }
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}



// ENHANCEMENT: Smart Scroll Toggle Button Styles - REPOSITIONED INSIDE chat messages

// Old style removed for fixed button

// FIXED: Smart scroll toggle button positioned outside scrollable area
.scroll-toggle-container-fixed {
  position: absolute;
  // right: 56%; // Position on the right side for better UX
  // bottom: 25%; // Place above the prompt bar, outside scrollable area
  bottom:180px;//extra
  z-index: 1000;
  pointer-events: none; // Allow clicks to pass through container
  transform: translateX(-50%);//extra
  display: flex;//extra
  justify-content: center;//extra
  width: 100%;//extra
  // width: 0;
  height: 0;
  // Positioned relative to .chat-wrapper which has position: relative

  .scroll-toggle-button {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    border: none;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    pointer-events: auto;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    backdrop-filter: blur(12px);
    position: absolute;
    overflow: hidden;

    // Enhanced Light theme styling with better contrast
    &.light {
      background: rgba(255, 255, 255, 0.92);
      border: 1px solid rgba(0, 0, 0, 0.08);
      color: #2c2c2c;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.12), 0 1px 3px rgba(0, 0, 0, 0.08);

      &:hover {
        background: rgba(255, 255, 255, 0.98);
        box-shadow: 0 4px 16px rgba(0, 0, 0, 0.16), 0 2px 6px rgba(0, 0, 0, 0.12);
        transform: translateY(-1px);
      }

      &:active {
        transform: translateY(0);
        box-shadow: 0 1px 4px rgba(0, 0, 0, 0.16);
      }
    }

    // Enhanced Dark theme styling with better contrast
    &.dark {
      background: rgba(28, 28, 30, 0.92);
      border: 1px solid rgba(255, 255, 255, 0.12);
      color: #f2f2f7;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3), 0 1px 3px rgba(0, 0, 0, 0.2);

      &:hover {
        background: rgba(28, 28, 30, 0.98);
        box-shadow: 0 4px 16px rgba(0, 0, 0, 0.4), 0 2px 6px rgba(0, 0, 0, 0.3);
        transform: translateY(-1px);
      }

      &:active {
        transform: translateY(0);
        box-shadow: 0 1px 4px rgba(0, 0, 0, 0.4);
      }
    }

    // Asset-based Icon styling
    .scroll-icon {
      transition: all 0.2s ease;
      width: 20px;
      height: 20px;

      // Theme-specific filters for SVG images
      &.light {
        filter: brightness(0) saturate(100%) invert(17%) sepia(0%) saturate(0%) hue-rotate(0deg) brightness(17%) contrast(100%); // Dark color for light theme
      }

      &.dark {
        filter: brightness(0) saturate(100%) invert(97%) sepia(3%) saturate(82%) hue-rotate(200deg) brightness(97%) contrast(96%); // Light color for dark theme
      }
    }

    // Hover effect for icon
    &:hover .scroll-icon {
      transform: scale(1.1);
    }

    // Ripple effect on click
    &::before {
      content: '';
      position: absolute;
      top: 50%;
      left: 50%;
      width: 0;
      height: 0;
      border-radius: 50%;
      background: rgba(156, 39, 176, 0.3);
      transform: translate(-50%, -50%);
      transition: width 0.3s ease, height 0.3s ease;
    }

    &:active::before {
      width: 100%;
      height: 100%;
    }

    // Focus styles for accessibility
    &:focus {
      outline: none;
      box-shadow: 0 0 0 3px rgba(156, 39, 176, 0.3);
    }

    // Animation for button appearance
    animation: scrollButtonFadeIn 0.3s ease-out;
  }


  @media (max-width: 768px) {
  .scroll-toggle-container-centered {
    display: none !important;
  }
}


  // Responsive adjustments for inside chat positioning
//   @media (max-width: 768px) {
//     bottom: 120px;
//     .scroll-toggle-button {
//       width: 40px;
//       height: 40px;
//       .scroll-icon {
//         width: 20px;
//         height: 20px;
//       }
//     }
//   }
//   @media (max-width: 480px) {
//     bottom: 100px;
//     .scroll-toggle-button {
//       width: 36px;
//       height: 36px;
//       .scroll-icon {
//         width: 18px;
//         height: 18px;
//       }
//     }
//   }
}

// Animation for scroll button appearance
@keyframes scrollButtonFadeIn {
  from {
    opacity: 0;
    transform: translateY(10px) scale(0.8);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}
