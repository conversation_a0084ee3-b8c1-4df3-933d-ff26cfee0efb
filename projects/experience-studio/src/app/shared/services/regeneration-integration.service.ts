import { Injectable, inject, DestroyRef } from '@angular/core';
import { BehaviorSubject, Observable, Subject, Subscription } from 'rxjs';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { filter, tap } from 'rxjs/operators';
import { createLogger } from '../utils/logger';
import { SequentialRegenerationService } from './sequential-regeneration.service';
import { CodeRegenerationUIService, CodeRegenerationProgress } from './code-regeneration-ui.service';
import { StepperStateService } from './stepper-state.service';
import { FileModel } from '../components/code-viewer/code-viewer.component';

/**
 * Regeneration Integration Service
 *
 * This service coordinates the perfect integration between all regeneration-related components:
 * - Sequential Regeneration Service (handles SSE events and file extraction)
 * - Code Regeneration UI Service (handles UI state management)
 * - Code Window Component (handles file updates and accordion creation)
 * - Chat Window Component (handles loading indicators and user interaction)
 * - Code Viewer Component (handles file override and display)
 * - Generation Accordion Component (handles version tracking)
 * - Vertical Stepper Component (isolated during regeneration)
 *
 * Key Features:
 * - Processes "code-regen" SSE events with progress: "CODE_GENERATION", status: "COMPLETED"
 * - Extracts files from metadata exactly like PAGES_GENERATED step
 * - Overrides previous codes and file models (complete replacement)
 * - Creates new version accordions for each regeneration
 * - Manages UI state transitions (loading → code tab → preview tab)
 * - Isolates stepper during regeneration to prevent interference
 */
@Injectable({
  providedIn: 'root'
})
export class RegenerationIntegrationService {
  private readonly destroyRef = inject(DestroyRef);
  private readonly logger = createLogger('RegenerationIntegrationService');

  // Core services
  private readonly sequentialRegenerationService = inject(SequentialRegenerationService);
  private readonly codeRegenerationUIService = inject(CodeRegenerationUIService);
  private readonly stepperStateService = inject(StepperStateService);

  // State management
  private readonly regenerationActiveSubject = new BehaviorSubject<boolean>(false);
  private readonly currentVersionSubject = new BehaviorSubject<number>(1);
  private readonly latestFilesSubject = new BehaviorSubject<FileModel[]>([]);
  private currentSessionId: string = '';
  private currentVersion = 1;

  // Event streams
  private readonly fileUpdateSubject = new Subject<{
    files: FileModel[];
    version: number;
    replaceAll: boolean;
  }>();
  private readonly accordionCreateSubject = new Subject<{
    files: string[];
    version: number;
    projectName: string;
    timestamp: Date;
  }>();
  private readonly uiStateUpdateSubject = new Subject<{
    phase: 'CODE_GENERATION' | 'BUILD' | 'DEPLOY';
    status: 'IN_PROGRESS' | 'COMPLETED' | 'FAILED';
    shouldSwitchTab?: 'code' | 'preview';
    shouldRefresh?: boolean;
    // ENHANCED: Progress description support
    description?: string;
    sessionId?: string;
    event?: string;
    isProgressDescription?: boolean;
    // ENHANCED: Error handling support
    isError?: boolean;
    errorMessage?: string;
    projectId?: string;
    jobId?: string;
    // ENHANCED: Vertical stepper support for regeneration failures
    showVerticalStepper?: boolean;
    stepperProgress?: string;
    stepperStatus?: 'IN_PROGRESS' | 'COMPLETED' | 'FAILED';
    stepperDescription?: string;
  }>();

  // ENHANCED: Error state management for regeneration failures
  private readonly errorStateSubject = new BehaviorSubject<{
    hasError: boolean;
    errorMessage: string;
    errorPhase: 'CODE_GENERATION' | 'BUILD' | 'DEPLOY' | null;
    projectId: string | null;
    jobId: string | null;
    isRetrying: boolean;
    canRetry: boolean;
  }>({
    hasError: false,
    errorMessage: '',
    errorPhase: null,
    projectId: null,
    jobId: null,
    isRetrying: false,
    canRetry: false
  });

  // FIXED: Chat window component reference for intro message creation
  private chatWindowComponent: any = null;

  // Public observables
  readonly regenerationActive$ = this.regenerationActiveSubject.asObservable();
  readonly currentVersion$ = this.currentVersionSubject.asObservable();
  readonly latestFiles$ = this.latestFilesSubject.asObservable();
  readonly fileUpdates$ = this.fileUpdateSubject.asObservable();
  readonly accordionCreate$ = this.accordionCreateSubject.asObservable();
  readonly uiStateUpdates$ = this.uiStateUpdateSubject.asObservable();
  // ENHANCED: Error state observable for regeneration failures
  readonly errorState$ = this.errorStateSubject.asObservable();

  // Subscription management
  private subscriptions = new Subscription();

  constructor() {
    this.logger.info('🔧 Regeneration Integration Service initialized');
    this.setupIntegrations();
  }

  /**
   * Setup integrations between all regeneration services
   * CRITICAL: Based on actual console SSE data structure
   */
  private setupIntegrations(): void {
    this.logger.info('🔗 Setting up regeneration service integrations based on console SSE data');

    // CRITICAL: Subscribe to sequential regeneration progress updates
    // This handles code-regen events with CODE_GENERATION COMPLETED containing files
    this.subscriptions.add(
      this.sequentialRegenerationService.progressUpdates$
        .pipe(
          takeUntilDestroyed(this.destroyRef),
          tap(update => this.logger.info('📨 Received progress update from sequential regeneration service:', {
            progress: update.progress,
            status: update.status,
            event: update.event,
            hasMetadata: !!update.metadata,
            deploymentCompleted: update.deploymentCompleted,
            hasIntroMessage: !!update.introMessage,
            messageId: update.messageId,
            willProcess: update.event === 'code-regen' || update.event === 'standalone-intro-card-created'
          })),
          // ENHANCED: Process both code-regen events AND intro card creation events
          filter(update => this.validateRegenerationEvent(update) || update.event === 'standalone-intro-card-created'),
          tap(update => this.logger.info('✅ Processing regeneration event:', {
            progress: update.progress,
            status: update.status,
            event: update.event,
            hasMetadata: !!update.metadata,
            deploymentCompleted: update.deploymentCompleted,
            hasIntroMessage: !!update.introMessage,
            messageId: update.messageId
          }))
        )
        .subscribe(update => {
          // FIXED: Handle intro message creation
          if (update.event === 'standalone-intro-card-created' && update.introMessage) {
            this.handleIntroMessageCreation(update);
          } else {
            this.processRegenerationProgress(update);
          }
        })
    );

    // CRITICAL: Subscribe to sequential regeneration file updates
    // This handles files extracted from CODE_GENERATION COMPLETED events
    this.subscriptions.add(
      this.sequentialRegenerationService.fileUpdates$
        .pipe(
          takeUntilDestroyed(this.destroyRef),
          tap(files => this.logger.info('📁 Received file updates from sequential regeneration service:', {
            fileCount: files?.length || 0,
            files: files?.map(f => f.name || f.fileName || 'Unknown') || [],
            regenerationActive: this.regenerationActiveSubject.value,
            willProcess: this.regenerationActiveSubject.value
          })),
          filter(() => {
            const isActive = this.regenerationActiveSubject.value;
            if (!isActive) {
              this.logger.warn('🚫 Skipping file updates - regeneration not active');
            }
            return isActive;
          }), // Only during active regeneration
          tap(files => this.logger.info('✅ Processing regeneration file updates from SSE:', {
            fileCount: files?.length || 0,
            files: files?.map(f => f.name || f.fileName || 'Unknown') || []
          }))
        )
        .subscribe(files => {
          this.processFileUpdates(files);
        })
    );

    // DISABLED: Progress descriptions subscription (using accordions instead)
    // this.subscriptions.add(
    //   this.sequentialRegenerationService.progressDescriptions$
    //     .pipe(
    //       takeUntilDestroyed(this.destroyRef),
    //       tap(description => this.logger.info('📝 Progress description disabled - using accordions')),
    //       filter(() => this.regenerationActiveSubject.value)
    //     )
    //     .subscribe(description => {
    //       // DISABLED: Using accordions instead
    //     })
    // );

    // Setup cleanup
    this.destroyRef.onDestroy(() => {
      this.subscriptions.unsubscribe();
      this.logger.info('🧹 Regeneration Integration Service cleaned up');
    });
  }

  /**
   * Start regeneration process
   */
  startRegeneration(): void {
    this.logger.info('🚀 Starting regeneration process');

    // Mark regeneration as active
    this.regenerationActiveSubject.next(true);

    // Isolate stepper during regeneration
    this.stepperStateService.setRegenerationActive(true);

    this.logger.info('🔒 Regeneration started - stepper isolated', {
      regenerationActive: this.regenerationActiveSubject.value,
      timestamp: new Date().toISOString()
    });
  }

  /**
   * Complete regeneration process - ENHANCED for comprehensive cleanup
   * This method ensures all regeneration states are properly reset
   */
  completeRegeneration(): void {
    this.logger.info('🏁 Completing regeneration process with comprehensive cleanup');

    // Mark regeneration as inactive
    this.regenerationActiveSubject.next(false);

    // Re-enable stepper
    this.stepperStateService.setRegenerationActive(false);

    this.logger.info('🔓 Regeneration completed - stepper re-enabled', {
      regenerationActive: this.regenerationActiveSubject.value,
      timestamp: new Date().toISOString()
    });
  }

  /**
   * ENHANCED: Validate regeneration event before processing
   * Ensures event type, progress, and status are valid for regeneration
   * Now includes FAILED status validation for error handling
   */
  private validateRegenerationEvent(update: any): boolean {
    // Check if event type is correct
    if (update.event !== 'code-regen') {
      this.logger.debug('⏭️ Skipping non-code-regen event:', {
        received: update.event,
        expected: 'code-regen',
        reason: 'Event type mismatch for regeneration'
      });
      return false;
    }

    // Check if progress and status are valid
    if (!update.progress || !update.status) {
      this.logger.warn('⚠️ Event missing progress or status:', {
        progress: update.progress,
        status: update.status,
        event: update.event,
        reason: 'Invalid event structure'
      });
      return false;
    }

    // ENHANCED: Validate status includes FAILED for error handling
    const validStatuses = ['IN_PROGRESS', 'COMPLETED', 'FAILED'];
    if (!validStatuses.includes(update.status)) {
      this.logger.warn('⚠️ Invalid status received:', {
        status: update.status,
        validStatuses,
        event: update.event,
        reason: 'Status not in valid list'
      });
      return false;
    }

    // Log successful validation
    this.logger.debug('✅ Event validation passed:', {
      event: update.event,
      progress: update.progress,
      status: update.status,
      hasMetadata: !!update.metadata,
      isError: update.status === 'FAILED'
    });

    return true;
  }



  /**
   * Process regeneration progress updates from SSE events
   * CRITICAL: Based on actual console SSE data structure
   * - CODE_GENERATION COMPLETED contains files in metadata
   * - DEPLOY COMPLETED triggers tab switch and iframe refresh
   * ENHANCED: Now handles FAILED status for error processing
   */
  private processRegenerationProgress(update: any): void {
    this.logger.info('🔄 Processing code-regen progress based on console SSE structure:', {
      progress: update.progress,
      status: update.status,
      event: update.event,
      hasMetadata: !!update.metadata,
      deploymentCompleted: update.deploymentCompleted,
      hasError: update.status === 'FAILED',
      errorMessage: update.errorMessage
    });

    // ENHANCED: Handle FAILED status events for error processing
    if (update.status === 'FAILED') {
      this.processRegenerationError(update);
      return; // Exit early for error processing
    }

    // CRITICAL: Handle CODE_GENERATION COMPLETED with files
    if (update.progress === 'CODE_GENERATION' && update.status === 'COMPLETED') {
      this.logger.info('📁 CODE_GENERATION COMPLETED - processing files from metadata');

      // Extract files from metadata exactly like console SSE structure
      if (update.metadata && Array.isArray(update.metadata)) {
        const filesMetadata = update.metadata.find((item: any) => item.type === 'files');
        if (filesMetadata && filesMetadata.data && Array.isArray(filesMetadata.data)) {
          this.logger.info('✅ Found files in CODE_GENERATION COMPLETED metadata:', {
            fileCount: filesMetadata.data.length,
            files: filesMetadata.data.map((f: any) => f.fileName || 'Unknown')
          });

          // Process files directly (they will be handled by file updates subscription)
        }
      }
    }

    // Emit UI state update based on console SSE flow with dynamic loading text
    this.uiStateUpdateSubject.next({
      phase: update.progress,
      status: update.status,
      shouldSwitchTab: this.determineSwitchTab(update.progress, update.status),
      shouldRefresh: update.progress === 'DEPLOY' && update.status === 'COMPLETED',
      // ENHANCED: Add dynamic loading text based on progress and status
      description: this.getLoadingTextForProgress(update.progress, update.status),
      sessionId: this.currentSessionId,
      event: update.event,
      isProgressDescription: true
    });

    // Handle completion based on console SSE structure
    if (update.deploymentCompleted || (update.progress === 'DEPLOY' && update.status === 'COMPLETED')) {
      this.logger.info('🎉 Regeneration deployment completed');

      // ENHANCED: Create accordion for completed regeneration
      this.createRegenerationAccordionOnDeployment();

      this.completeRegeneration();
    }
  }

  /**
   * ENHANCED: Process regeneration error events
   * Handles FAILED status events and updates error state for UI display
   * ENHANCED: Coordinates with SSE service for connection cleanup
   */
  private processRegenerationError(update: any): void {
    this.logger.error('❌ Processing regeneration error:', {
      progress: update.progress,
      status: update.status,
      errorMessage: update.errorMessage,
      log: update.log,
      projectId: update.projectId,
      jobId: update.jobId
    });

    // Extract error message from update
    const errorMessage = this.extractErrorMessage(update);
    const errorPhase = this.mapProgressToPhase(update.progress);

    // ENHANCED: Coordinate with SSE service to prevent reconnection
    // The SSE service will automatically detect FAILED events and prevent reconnection
    this.logger.info('🚫 FAILED event detected - SSE service will prevent reconnection automatically');

    // Update error state
    this.errorStateSubject.next({
      hasError: true,
      errorMessage,
      errorPhase,
      projectId: update.projectId || null,
      jobId: update.jobId || null,
      isRetrying: false,
      canRetry: this.canRetryError(update.progress)
    });

    // Emit UI state update for error display
    this.uiStateUpdateSubject.next({
      phase: errorPhase,
      status: 'FAILED',
      shouldSwitchTab: undefined,
      shouldRefresh: false,
      description: `Error during ${errorPhase.toLowerCase().replace('_', ' ')}`,
      sessionId: this.currentSessionId,
      event: update.event,
      isProgressDescription: false,
      isError: true,
      errorMessage,
      projectId: update.projectId,
      jobId: update.jobId
    });

    this.logger.info('🚨 Error state updated and UI notified:', {
      errorPhase,
      errorMessage: errorMessage.substring(0, 100) + '...',
      canRetry: this.canRetryError(update.progress)
    });

    // ENHANCED: Create error accordion for display in chat window
    this.createErrorAccordion(errorMessage, errorPhase, update.projectId, update.jobId);
  }

  /**
   * Process file updates from regeneration
   * CRITICAL: Based on actual console SSE data structure
   * - Files come from CODE_GENERATION COMPLETED events
   * - Complete replacement for regeneration (not append)
   */
  private processFileUpdates(files: FileModel[]): void {
    if (!files || files.length === 0) {
      this.logger.warn('⚠️ No files received from CODE_GENERATION COMPLETED');
      return;
    }

    this.logger.info('📁 Processing file updates from CODE_GENERATION COMPLETED:', {
      fileCount: files.length,
      files: files.map(f => f.name || f.fileName || 'Unknown'),
      source: 'code-regen SSE event'
    });

    // Increment version
    const newVersion = this.currentVersionSubject.value + 1;
    this.currentVersionSubject.next(newVersion);

    // Update latest files
    this.latestFilesSubject.next(files);

    // CRITICAL: Emit file update event with complete replacement flag for regeneration
    this.fileUpdateSubject.next({
      files,
      version: newVersion,
      replaceAll: true // Critical: Complete replacement for regeneration (not append)
    });

    // CRITICAL: Create accordion for regeneration with proper version tracking
    this.createRegenerationAccordion(files, newVersion);

    this.logger.info('✅ File updates processed for regeneration from SSE:', {
      version: newVersion,
      fileCount: files.length,
      replaceAll: true,
      source: 'CODE_GENERATION COMPLETED'
    });
  }

  /**
   * Handle code viewer updates
   */
  private handleCodeViewerUpdate(files: any[]): void {
    this.logger.info('🖥️ Handling code viewer update:', { fileCount: files?.length || 0 });

    // Convert to FileModel format if needed
    const fileModels = this.convertToFileModels(files);
    this.processFileUpdates(fileModels);
  }

  /**
   * Handle accordion updates
   */
  private handleAccordionUpdate(accordionData: any): void {
    this.logger.info('📋 Handling accordion update:', accordionData);

    if (accordionData.files && accordionData.files.length > 0) {
      // Extract file names
      const fileNames = accordionData.files.map((file: any) =>
        file.fileName || file.name || file.path || 'Unknown file'
      );

      // Emit accordion creation event
      this.accordionCreateSubject.next({
        files: fileNames,
        version: accordionData.version,
        projectName: 'Regenerated Project', // Could be made configurable
        timestamp: new Date()
      });
    }
  }

  /**
   * Convert various file formats to FileModel
   */
  private convertToFileModels(files: any[]): FileModel[] {
    if (!files || !Array.isArray(files)) {
      return [];
    }

    return files.map(file => ({
      name: file.fileName || file.name || file.path || 'Unknown file',
      fileName: file.fileName || file.name || file.path,
      type: 'file' as const,
      content: file.content || file.code || file.data || '',
    }));
  }

  /**
   * Create regeneration accordion with proper version tracking
   * CRITICAL: Based on actual console SSE data structure
   */
  private createRegenerationAccordion(files: FileModel[], version: number): void {
    this.logger.info('📋 Creating regeneration accordion from CODE_GENERATION COMPLETED:', {
      fileCount: files.length,
      version: version,
      files: files.map(f => f.name || f.fileName || 'Unknown')
    });

    // Create accordion data exactly like console SSE structure
    const accordionInfo = {
      files: files.map(f => f.name || f.fileName || 'Unknown'),
      version: version,
      projectName: 'Regenerated Project',
      timestamp: new Date()
    };

    // Emit accordion creation event
    this.accordionCreateSubject.next(accordionInfo);

    this.logger.info('✅ Regeneration accordion created:', accordionInfo);
  }

  /**
   * ENHANCED: Create accordion when deployment completes
   */
  private createRegenerationAccordionOnDeployment(): void {
    const latestFiles = this.latestFilesSubject.value;
    const currentVersion = this.currentVersionSubject.value;

    this.logger.info('📋 Attempting to create accordion for deployment completion:', {
      hasFiles: !!latestFiles,
      fileCount: latestFiles?.length || 0,
      version: currentVersion,
      files: latestFiles?.map(f => f.fileName || f.name || 'Unknown') || []
    });

    if (latestFiles && latestFiles.length > 0) {
      this.logger.info('✅ Creating accordion for deployment completion with files');

      // Create accordion with latest files
      this.createRegenerationAccordion(latestFiles, currentVersion);
    } else {
      this.logger.warn('⚠️ No files available for accordion creation on deployment - checking alternative sources');

      // Try to get files from the last progress update
      this.tryCreateAccordionFromLastUpdate();
    }
  }

  /**
   * ENHANCED: Try to create accordion from last update if no files in latestFiles
   */
  private tryCreateAccordionFromLastUpdate(): void {
    // For now, create a simple accordion indicating regeneration completed
    const accordionInfo = {
      files: ['Regeneration completed successfully'],
      version: this.currentVersionSubject.value,
      projectName: 'Regenerated Project',
      timestamp: new Date()
    };

    this.logger.info('📋 Creating fallback accordion for regeneration completion');
    this.accordionCreateSubject.next(accordionInfo);
  }

  /**
   * ENHANCED: Create error accordion for regeneration failures
   * Creates an error accordion that displays in the chat window with retry functionality
   */
  private createErrorAccordion(
    errorMessage: string,
    errorPhase: 'CODE_GENERATION' | 'BUILD' | 'DEPLOY',
    projectId?: string,
    jobId?: string
  ): void {
    this.logger.info('🚨 Creating error accordion for regeneration failure:', {
      errorPhase,
      errorMessage: errorMessage.substring(0, 100) + '...',
      hasProjectId: !!projectId,
      hasJobId: !!jobId
    });

    // Create error accordion that will be displayed in chat window
    const errorAccordionInfo = {
      files: [], // No files for error accordions
      version: this.currentVersionSubject.value,
      projectName: `${errorPhase.toLowerCase().replace('_', ' ')} Failed`,
      timestamp: new Date(),
      // ENHANCED: Error-specific properties
      isError: true,
      errorMessage,
      errorPhase,
      projectId,
      jobId
    };

    // Emit accordion creation event - chat window will convert this to GenerationResult
    this.accordionCreateSubject.next(errorAccordionInfo);

    this.logger.info('✅ Error accordion creation event emitted');
  }

  /**
   * DISABLED: Process progress description (using accordions instead)
   */
  private processProgressDescription(description: any): void {
    this.logger.info('📝 Progress description processing disabled - using accordions instead');
  }

  // DISABLED: Mapping methods (not needed for accordion-based approach)

  /**
   * Determine which tab to switch to based on progress and status
   */
  private determineSwitchTab(progress: string, status: string): 'code' | 'preview' | undefined {
    if (progress === 'CODE_GENERATION' && status === 'COMPLETED') {
      return 'code'; // Switch to code tab to show new files
    }
    if (progress === 'DEPLOY' && status === 'COMPLETED') {
      return 'preview'; // Switch to preview tab to show deployed app
    }
    return undefined;
  }

  /**
   * Get dynamic loading text based on progress and status
   * ENHANCED: Provides specific loading messages for each regeneration phase
   */
  private getLoadingTextForProgress(progress: string, status: string): string {
    // Default to initial state
    if (!progress || progress === 'CODE_GENERATION') {
      return 'Generating...';
    }

    // BUILD phase
    if (progress === 'BUILD') {
      if (status === 'IN_PROGRESS' || status === 'COMPLETED') {
        return 'Building...';
      }
    }

    // DEPLOY phase
    if (progress === 'DEPLOY') {
      if (status === 'IN_PROGRESS') {
        return 'Deploying...';
      }
    }

    // Fallback for unknown states
    return 'Processing...';
  }

  /**
   * Get current regeneration state
   */
  isRegenerationActive(): boolean {
    return this.regenerationActiveSubject.value;
  }

  /**
   * Get current version
   */
  getCurrentVersion(): number {
    return this.currentVersionSubject.value;
  }

  /**
   * Get latest files
   */
  getLatestFiles(): FileModel[] {
    return this.latestFilesSubject.value;
  }

  /**
   * Set chat window component reference for intro message creation
   * FIXED: Allows intro messages to be displayed as AI cards
   */
  public setChatWindowComponent(chatWindowComponent: any): void {
    this.chatWindowComponent = chatWindowComponent;
    this.logger.info('🔗 Chat window component reference set for intro message creation');
  }

  /**
   * ENHANCED: Trigger chat-window retry functionality
   * Re-enables prompt bar and appends error message with "fix it"
   * CRITICAL: Coordinates with chat-window component for retry workflow
   */
  public triggerChatWindowRetry(errorMessage: string): void {
    this.logger.info('🔄 Triggering chat-window retry functionality:', {
      errorMessageLength: errorMessage.length,
      errorPreview: errorMessage.substring(0, 100) + '...',
      hasChatWindow: !!this.chatWindowComponent
    });

    if (this.chatWindowComponent) {
      // CRITICAL: Re-enable prompt bar by setting code generation as complete
      if (typeof this.chatWindowComponent.setCodeGenerationComplete === 'function') {
        this.chatWindowComponent.setCodeGenerationComplete(true);
        this.logger.info('✅ Prompt bar re-enabled via setCodeGenerationComplete');
      }

      // CRITICAL: Append error message with "fix it" to prompt bar
      if (typeof this.chatWindowComponent.appendErrorToPrompt === 'function') {
        const retryPrompt = `${errorMessage} - fix it`;
        this.chatWindowComponent.appendErrorToPrompt(retryPrompt);
        this.logger.info('✅ Error message appended to prompt bar:', {
          retryPromptLength: retryPrompt.length,
          retryPromptPreview: retryPrompt.substring(0, 100) + '...'
        });
      } else {
        // Fallback: Set text value directly if appendErrorToPrompt doesn't exist
        if (typeof this.chatWindowComponent.setTextValue === 'function') {
          const retryPrompt = `${errorMessage} - fix it`;
          this.chatWindowComponent.setTextValue(retryPrompt);
          this.logger.info('✅ Error message set as text value (fallback):', {
            retryPromptLength: retryPrompt.length,
            retryPromptPreview: retryPrompt.substring(0, 100) + '...'
          });
        }
      }

      // CRITICAL: Hide prompt bar loading indicator if visible
      if (typeof this.chatWindowComponent.hidePromptBarLoadingPublic === 'function') {
        this.chatWindowComponent.hidePromptBarLoadingPublic();
        this.logger.info('✅ Prompt bar loading indicator hidden');
      }
    } else {
      this.logger.warn('⚠️ Cannot trigger chat-window retry - no chat window component reference');
    }
  }

  /**
   * Handle intro message creation from sequential regeneration service
   * FIXED: Creates AI cards for intro messages
   */
  private handleIntroMessageCreation(update: any): void {
    this.logger.info('💬 Handling intro message creation:', {
      messageId: update.messageId,
      introTextLength: update.introMessage?.length || 0,
      hasChatWindow: !!this.chatWindowComponent
    });

    if (this.chatWindowComponent && this.chatWindowComponent.addStandaloneAIMessage && update.introMessage) {
      // Create AI card with the intro message
      const createdMessageId = this.chatWindowComponent.addStandaloneAIMessage(
        update.introMessage,
        update.messageId
      );

      this.logger.info('✅ Intro message AI card created successfully:', {
        originalMessageId: update.messageId,
        createdMessageId,
        introTextLength: update.introMessage.length
      });
    } else {
      this.logger.warn('⚠️ Cannot create intro message AI card:', {
        hasChatWindow: !!this.chatWindowComponent,
        hasAddMethod: !!(this.chatWindowComponent?.addStandaloneAIMessage),
        hasIntroMessage: !!update.introMessage
      });
    }
  }

  /**
   * ENHANCED: Extract error message from SSE update
   * Handles different error message formats and provides fallback
   * CRITICAL: Prioritizes data.log field as specified in requirements
   */
  private extractErrorMessage(update: any): string {
    // CRITICAL: First try to extract from data.log field as specified in requirements
    if (update.log && typeof update.log === 'string') {
      try {
        // Try to parse JSON log
        const logData = JSON.parse(update.log);
        if (logData.message) {
          this.logger.info('📋 Extracted error message from parsed log data:', {
            messageLength: logData.message.length,
            messagePreview: logData.message.substring(0, 100) + '...'
          });
          return logData.message;
        }
        this.logger.info('📋 Using raw log data as error message:', {
          logLength: update.log.length,
          logPreview: update.log.substring(0, 100) + '...'
        });
        return update.log;
      } catch {
        // Return raw log if not JSON
        this.logger.info('📋 Using raw log data as error message (not JSON):', {
          logLength: update.log.length,
          logPreview: update.log.substring(0, 100) + '...'
        });
        return update.log;
      }
    }

    // Try to extract from errorMessage field as fallback
    if (update.errorMessage && typeof update.errorMessage === 'string') {
      this.logger.info('📋 Using errorMessage field as fallback:', {
        messageLength: update.errorMessage.length,
        messagePreview: update.errorMessage.substring(0, 100) + '...'
      });
      return update.errorMessage.trim();
    }

    // Handle complete failure case
    if (update.status === 'FAILED' && update.progress === 'FAILED') {
      return 'Error with the application. You can retry to fix it.';
    }

    // Default error message based on progress phase
    const phase = this.mapProgressToPhase(update.progress);
    return `${phase.toLowerCase().replace('_', ' ')} failed. Please try again.`;
  }

  /**
   * ENHANCED: Map progress string to phase enum
   * Handles different progress values and provides fallback
   */
  private mapProgressToPhase(progress: string): 'CODE_GENERATION' | 'BUILD' | 'DEPLOY' {
    if (!progress || typeof progress !== 'string') {
      return 'CODE_GENERATION'; // Default fallback
    }

    const upperProgress = progress.toUpperCase();

    if (upperProgress.includes('CODE') || upperProgress.includes('GENERATION')) {
      return 'CODE_GENERATION';
    } else if (upperProgress.includes('BUILD')) {
      return 'BUILD';
    } else if (upperProgress.includes('DEPLOY')) {
      return 'DEPLOY';
    }

    // Default to CODE_GENERATION for unknown progress
    return 'CODE_GENERATION';
  }

  /**
   * ENHANCED: Determine if error can be retried based on progress phase
   * All regeneration errors are retryable using the /build/error endpoint
   */
  private canRetryError(progress: string): boolean {
    // All regeneration errors are retryable
    return true;
  }

  /**
   * ENHANCED: Set retry state for error handling
   * Updates error state to show retry in progress
   */
  setRetryState(isRetrying: boolean): void {
    const currentState = this.errorStateSubject.value;
    if (currentState.hasError) {
      this.errorStateSubject.next({
        ...currentState,
        isRetrying
      });

      this.logger.info('🔄 Retry state updated:', { isRetrying });
    }
  }

  /**
   * ENHANCED: Clear error state
   * Resets error state when regeneration succeeds or is manually cleared
   */
  clearErrorState(): void {
    this.errorStateSubject.next({
      hasError: false,
      errorMessage: '',
      errorPhase: null,
      projectId: null,
      jobId: null,
      isRetrying: false,
      canRetry: false
    });

    this.logger.info('✅ Error state cleared');
  }
}
